<?php

namespace Modules\ChatBot\Http\Controllers\Auth;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Http\Controllers\Controller;
use Modules\ChatBot\Http\Requests\CreateMessageRequest;
use Modules\ChatBot\Http\Requests\UpdateMessageRequest;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Services\MessageService;
use Modules\ChatBot\Services\StreamingService;
use Modules\ChatBot\Facades\MessageFacade;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\MessageStatus;
use Modules\Core\Traits\ResponseTrait;
use Symfony\Component\HttpFoundation\StreamedResponse;

class MessageController extends Controller
{
    use ResponseTrait;

    public function __construct() {

    }

    /**
     * Display a listing of conversation messages.
     */
    public function index(Request $request, string $conversationUuid): JsonResponse
    {
        $messages = MessageFacade::getConversationMessages($conversationUuid);
        return $this->successResponse($messages->items()->reverse()->values(), 'Messages retrieved successfully.');
    }

    /**
     * Store a newly created message.
     */
    public function store(CreateMessageRequest $request): JsonResponse
    {
        try {
            $user = auth()->user();
            $data = $request->validated();
            
            $conversation = Conversation::where('uuid', $data['conversation_uuid'])->firstOrFail();

            // Check if user can write to this conversation
            if (!$this->canWriteToConversation($conversation, $user)) {
                return $this->errorResponse('Access denied', 403);
            }

            // Convert conversation_uuid to conversation_id
            $data['conversation_id'] = $conversation->id;
            unset($data['conversation_uuid']);

            $message = $this->messageService->createUserMessage($data);

            return $this->successResponse(
                $message->load('conversation'),
                'Message created successfully',
                201
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Display the specified message.
     */
    public function show(string $uuid): JsonResponse
    {
        try {
            $user = auth()->user();
            $message = Message::where('uuid', $uuid)->firstOrFail();

            // Check if user can access this message
            if (!$this->canAccessMessage($message, $user)) {
                return $this->errorResponse('Access denied', 403);
            }

            $message->load('conversation');

            return $this->successResponse(
                $message,
                'Message retrieved successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Update the specified message.
     */
    public function update(UpdateMessageRequest $request, string $uuid): JsonResponse
    {
        $request->validate([
            'content' => 'sometimes|required|string|max:50000',
            'quality_score' => 'nullable|integer|min:1|max:5',
            'is_helpful' => 'nullable|boolean',
            'feedback' => 'nullable|string|max:1000',
        ]);

        try {
            $user = auth()->user();
            $message = Message::where('uuid', $uuid)->firstOrFail();

            // Check if user can edit this message
            if (!$this->canEditMessage($message, $user)) {
                return $this->errorResponse('Access denied', 403);
            }

            $data = $request->validated();
            
            // Handle feedback in metadata
            if (isset($data['feedback'])) {
                $data['metadata'] = array_merge($message->metadata ?? [], [
                    'user_feedback' => $data['feedback'],
                    'feedback_at' => now()->toISOString(),
                ]);
                unset($data['feedback']);
            }

            $updatedMessage = $this->messageService->updateMessage($message, $data);

            return $this->successResponse(
                $updatedMessage,
                'Message updated successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Remove the specified message.
     */
    public function destroy(string $uuid): JsonResponse
    {
        try {
            $user = auth()->user();
            $message = Message::where('uuid', $uuid)->firstOrFail();

            // Check if user can delete this message
            if (!$this->canDeleteMessage($message, $user)) {
                return $this->errorResponse('Access denied', 403);
            }

            $this->messageService->deleteMessage($message);

            return $this->successResponse(null, 'Message deleted successfully');

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Generate AI response for a conversation.
     */
    public function generateResponse(Request $request, string $conversationUuid): JsonResponse
    {
        $request->validate([
            'temperature' => 'nullable|numeric|min:0|max:2',
            'max_tokens' => 'nullable|integer|min:1|max:8192',
        ]);

        try {
            $user = auth()->user();
            $conversation = Conversation::where('uuid', $conversationUuid)->firstOrFail();

            // Check if user can use this bot
            if (!$this->canUseBot($conversation, $user)) {
                return $this->errorResponse('Access denied', 403);
            }

            $options = $request->only(['temperature', 'max_tokens']);
            $aiMessage = $this->messageService->generateAIResponse($conversation, $options);

            return $this->successResponse(
                $aiMessage,
                'AI response generated successfully',
                201
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Send message and get AI response.
     */
    public function sendAndRespond(CreateMessageRequest $request): JsonResponse
    {
        try {
            $user = auth()->user();
            $data = $request->validated();
            
            $conversation = Conversation::where('uuid', $data['conversation_uuid'])->firstOrFail();

            // Check permissions
            if (!$this->canWriteToConversation($conversation, $user) || 
                !$this->canUseBot($conversation, $user)) {
                return $this->errorResponse('Access denied', 403);
            }

            // Convert conversation_uuid to conversation_id
            $data['conversation_id'] = $conversation->id;
            unset($data['conversation_uuid']);

            // Create user message
            $userMessage = $this->messageService->createUserMessage($data);

            // Generate AI response
            $options = $request->only(['temperature', 'max_tokens']);
            $aiMessage = $this->messageService->generateAIResponse($conversation, $options);

            return $this->successResponse(
                [
                    'user_message' => $userMessage,
                    'ai_message' => $aiMessage,
                ],
                'Message sent and response generated successfully',
                201
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Generate streaming AI response for a conversation.
     */
    public function generateStreamingResponse(Request $request, string $conversationUuid): StreamedResponse
    {
        $request->validate([
            'temperature' => 'nullable|numeric|min:0|max:2',
            'max_tokens' => 'nullable|integer|min:1|max:8192',
        ]);

        try {
            $user = auth()->user();
            $conversation = Conversation::where('uuid', $conversationUuid)->firstOrFail();

            // Check permissions
            if (!$this->canUseBot($conversation, $user)) {
                return response()->json(['error' => 'Access denied'], 403);
            }

            // Check if streaming is supported
            if (!$this->streamingService->isStreamingSupported($conversation->bot)) {
                return response()->json([
                    'error' => 'Streaming is not supported for this bot model.'
                ], 400);
            }

            $options = $request->only(['temperature', 'max_tokens']);
            return $this->streamingService->generateStreamingResponse($conversation, $options);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send a message and get streaming AI response.
     */
    public function sendAndStreamResponse(CreateMessageRequest $request): StreamedResponse
    {
        try {
            $user = auth()->user();
            $data = $request->validated();
            
            $conversation = Conversation::where('uuid', $data['conversation_uuid'])->firstOrFail();

            // Check permissions
            if (!$this->canWriteToConversation($conversation, $user) || 
                !$this->canUseBot($conversation, $user)) {
                return response()->json(['error' => 'Access denied'], 403);
            }

            // Check if streaming is supported
            if (!$this->streamingService->isStreamingSupported($conversation->bot)) {
                return response()->json([
                    'error' => 'Streaming is not supported for this bot model.'
                ], 400);
            }

            // Convert conversation_uuid to conversation_id
            $data['conversation_id'] = $conversation->id;
            unset($data['conversation_uuid']);

            // Create user message
            $userMessage = $this->messageService->createUserMessage($data);

            // Generate streaming response
            $options = $request->only(['temperature', 'max_tokens']);
            return $this->streamingService->generateStreamingResponse($conversation, $options);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Retry a failed message.
     */
    public function retry(string $uuid): JsonResponse
    {
        try {
            $user = auth()->user();
            $message = Message::where('uuid', $uuid)->firstOrFail();

            // Check if user can edit this message
            if (!$this->canEditMessage($message, $user)) {
                return $this->errorResponse('Access denied', 403);
            }

            $retriedMessage = $this->messageService->retryMessage($message);

            return $this->successResponse(
                $retriedMessage,
                'Message retry initiated successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Rate a message.
     */
    public function rate(Request $request, string $uuid): JsonResponse
    {
        $request->validate([
            'quality_score' => 'required|integer|min:1|max:5',
            'is_helpful' => 'required|boolean',
            'feedback' => 'nullable|string|max:1000',
        ]);

        try {
            $user = auth()->user();
            $message = Message::where('uuid', $uuid)->firstOrFail();

            // Check if user can access this message
            if (!$this->canAccessMessage($message, $user)) {
                return $this->errorResponse('Access denied', 403);
            }

            $data = $request->validated();
            
            // Handle feedback in metadata
            if (isset($data['feedback'])) {
                $data['metadata'] = array_merge($message->metadata ?? [], [
                    'user_feedback' => $data['feedback'],
                    'feedback_at' => now()->toISOString(),
                    'rated_by' => $user->id,
                ]);
                unset($data['feedback']);
            }

            $ratedMessage = $this->messageService->updateMessage($message, $data);

            return $this->successResponse(
                $ratedMessage,
                'Message rated successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Permission check methods.
     */
    private function canAccessConversation(Conversation $conversation, $user): bool
    {
        return $conversation->owner_id === $user->id &&
               $conversation->owner_type === get_class($user) ||
               $conversation->bot->canBeAccessedBy($user->id);
    }

    private function canWriteToConversation(Conversation $conversation, $user): bool
    {
        return $conversation->owner_id === $user->id &&
               $conversation->owner_type === get_class($user) ||
               $conversation->bot->userCan($user->id, 'write');
    }

    private function canUseBot(Conversation $conversation, $user): bool
    {
        return $conversation->bot->canBeAccessedBy($user->id);
    }

    private function canAccessMessage(Message $message, $user): bool
    {
        return $this->canAccessConversation($message->conversation, $user);
    }

    private function canEditMessage(Message $message, $user): bool
    {
        return $this->canWriteToConversation($message->conversation, $user);
    }

    private function canDeleteMessage(Message $message, $user): bool
    {
        return $message->conversation->user_id === $user->id && 
               $message->conversation->user_type === get_class($user) ||
               $message->conversation->bot->userCan($user->id, 'admin');
    }

    /**
     * Get messages by role.
     */
    public function messagesByRole(Request $request, string $conversationUuid, string $role): JsonResponse
    {
        $request->validate([
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        try {
            $user = auth()->user();
            $conversation = Conversation::where('uuid', $conversationUuid)->firstOrFail();

            if (!$this->canAccessConversation($conversation, $user)) {
                return $this->errorResponse('Access denied', 403);
            }

            $perPage = $request->get('per_page', 50);
            $messages = $this->messageService->getConversationMessages(
                $conversationUuid,
                ['role' => $role],
                $perPage
            );

            return $this->paginatedResponse(
                $messages,
                "Messages with role '{$role}' retrieved successfully"
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Get failed messages.
     */
    public function failedMessages(Request $request, string $conversationUuid): JsonResponse
    {
        $request->validate([
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        try {
            $user = auth()->user();
            $conversation = Conversation::where('uuid', $conversationUuid)->firstOrFail();

            if (!$this->canAccessConversation($conversation, $user)) {
                return $this->errorResponse('Access denied', 403);
            }

            $perPage = $request->get('per_page', 50);
            $messages = $this->messageService->getConversationMessages(
                $conversationUuid,
                ['status' => 'failed'],
                $perPage
            );

            return $this->paginatedResponse(
                $messages,
                'Failed messages retrieved successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Bulk retry failed messages.
     */
    public function bulkRetry(Request $request, string $conversationUuid): JsonResponse
    {
        $request->validate([
            'message_ids' => 'nullable|array',
            'message_ids.*' => 'string|exists:messages,uuid',
        ]);

        try {
            $user = auth()->user();
            $conversation = Conversation::where('uuid', $conversationUuid)->firstOrFail();

            if (!$this->canWriteToConversation($conversation, $user)) {
                return $this->errorResponse('Access denied', 403);
            }

            $messageIds = $request->get('message_ids');
            $results = [];

            if ($messageIds) {
                // Retry specific messages
                foreach ($messageIds as $messageUuid) {
                    $message = Message::where('uuid', $messageUuid)
                                    ->where('conversation_id', $conversation->id)
                                    ->first();

                    if ($message && $this->canEditMessage($message, $user)) {
                        try {
                            $retriedMessage = $this->messageService->retryMessage($message);
                            $results[] = [
                                'message_id' => $messageUuid,
                                'success' => true,
                                'message' => $retriedMessage,
                            ];
                        } catch (\Exception $e) {
                            $results[] = [
                                'message_id' => $messageUuid,
                                'success' => false,
                                'error' => $e->getMessage(),
                            ];
                        }
                    }
                }
            } else {
                // Retry all failed messages in conversation
                $failedMessages = Message::where('conversation_id', $conversation->id)
                                        ->where('status', MessageStatus::FAILED)
                                        ->get();

                foreach ($failedMessages as $message) {
                    try {
                        $retriedMessage = $this->messageService->retryMessage($message);
                        $results[] = [
                            'message_id' => $message->uuid,
                            'success' => true,
                            'message' => $retriedMessage,
                        ];
                    } catch (\Exception $e) {
                        $results[] = [
                            'message_id' => $message->uuid,
                            'success' => false,
                            'error' => $e->getMessage(),
                        ];
                    }
                }
            }

            return $this->successResponse(
                $results,
                'Bulk retry completed'
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Get message statistics.
     */
    public function stats(): JsonResponse
    {
        try {
            $user = auth()->user();

            $stats = [
                'total_messages' => Message::whereHas('conversation', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->count(),

                'messages_by_role' => Message::whereHas('conversation', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })
                ->selectRaw('role, COUNT(*) as count')
                ->groupBy('role')
                ->pluck('count', 'role')
                ->toArray(),

                'messages_by_status' => Message::whereHas('conversation', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })
                ->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray(),

                'total_tokens' => Message::whereHas('conversation', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->sum('total_tokens'),

                'total_cost' => Message::whereHas('conversation', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->sum('cost'),

                'average_response_time' => Message::whereHas('conversation', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })
                ->where('role', MessageRole::ASSISTANT)
                ->whereNotNull('response_time_ms')
                ->avg('response_time_ms'),
            ];

            return $this->successResponse(
                $stats,
                'Message statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Get pagination metadata.
     */
    private function getPaginationMeta(LengthAwarePaginator $paginator): array
    {
        return [
            'current_page' => $paginator->currentPage(),
            'per_page' => $paginator->perPage(),
            'total' => $paginator->total(),
            'last_page' => $paginator->lastPage(),
            'from' => $paginator->firstItem(),
            'to' => $paginator->lastItem(),
        ];
    }
}
