<?php

namespace Modules\ChatBot\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Enums\ConversationStatus;
use Mo<PERSON>les\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\MessageStatus;
use Modules\ChatBot\Enums\ContentType;
use Modules\Core\Exceptions\BusinessException;

class ConversationService
{
    public function __construct(

    ) {

    }

    /**
     * Create a new conversation with user permission validation.
     */
    public function createOrUpdateConversation(array $data): Conversation|string
    {
        // Convert bot_uuid to bot_id if provided
        if (isset($data['bot_uuid']) && !isset($data['bot_id'])) {
            $bot = Bot::where('uuid', $data['bot_uuid'])->firstOrFail();
            $data['bot_id'] = $bot->id;
        } else {
            // Validate bot exists and is active
            $bot = Bot::findOrFail($data['bot_id']);
        }

        if (!$bot->isActive()) {
            return __('Bot is not active and cannot be used for conversations.');
        }

        // Set owner_id if not provided
        if (!isset($data['owner_id'])) {
            $data['owner_id'] = auth()->id();
        }

        // Check if user can access this bot
        if (!$bot->canBeAccessedBy($data['owner_id'])) {
            return __('You do not have permission to use this bot.');
        }

        // Generate title if not provided
        if (empty($data['title'])) {
            $data['title'] = $this->generateConversationTitle($bot);
        }

        $conversation = Conversation::updateOrCreate([
            'uuid' => $data['conversation_uuid'] ?? null,
            'bot_id' => $data['bot_id'],
            'owner_id' => auth()->id(),
            'owner_type' => get_class(auth()->user()),
        ], [
            'title' => $data['title'],
            'status' => ConversationStatus::ACTIVE,
        ]);

        // Create greeting message if bot has one
        if (empty($data['conversation_uuid']) && $bot->hasGreetingMessage()) {
            $this->createGreetingMessage($conversation, $bot);
        }


        return $conversation->load(['bot', 'owner']);
    }


    /**
     * Archive a conversation.
     */
    public function archiveConversation(Conversation $conversation): Conversation
    {
        if (!$conversation->status->canBeArchived()) {
            throw new BusinessException('Conversation cannot be archived in its current status.');
        }

        $conversation->archive();
        return $conversation->fresh(['bot', 'user']);
    }

    /**
     * Delete a conversation and all its messages.
     */
    public function deleteConversation(Conversation $conversation): bool
    {
        if (!$conversation->canBeDeleted()) {
            throw new BusinessException('Conversation cannot be deleted in its current status.');
        }

        try {
            DB::beginTransaction();
            // Delete all messages first
            $conversation->messages()->delete();
            $conversation->delete();
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            return false;
            logger()->error('Failed to delete conversation messages: ' . $e->getMessage());
        }
    }

    /**
     * Get conversation statistics.
     */
    public function getConversationStats(Conversation $conversation): array
    {
        $messages = $conversation->messages();

        return [
            'total_messages' => $messages->count(),
            'user_messages' => $messages->fromUser()->count(),
            'assistant_messages' => $messages->fromAssistant()->count(),
            'total_tokens' => $messages->sum('total_tokens'),
            'total_cost' => $messages->sum('cost'),
            'average_response_time' => $messages->fromAssistant()->avg('response_time_ms'),
            'first_message_at' => $messages->oldest()->first()?->created_at,
            'last_message_at' => $messages->latest()->first()?->created_at,
        ];
    }

    /**
     * Generate a conversation title based on the bot.
     */
    private function generateConversationTitle(Bot $bot): string
    {
        $timestamp = now()->format('Y-m-d H:i');
        return "Cuộc trò chuyện với {$bot->name} - {$timestamp}";
    }

    /**
     * Get active conversations count for a user.
     */
    public function getActiveConversationsCount(int $userId, string $userType): int
    {
        return Conversation::forOwner($userId, $userType)
            ->active()
            ->count();
    }

    /**
     * Get conversations for a user.
     */
    public function getUserConversations(array $data = []): Collection {
        // Nếu không có bot_uuid thì không trả về conversation nào
        if (empty($data['bot_uuid'])) {
            return collect();
        }

        return Conversation::query()
            ->with([
                'bot:id,uuid,name,logo',
                'owner:id,uuid,first_name,last_name,full_name,avatar',
                'messages' => function ($query) {
                    $query->select(['id', 'uuid', 'conversation_id', 'role', 'content', 'content_type', 'created_at'])
                        ->completed();
                }
            ])
            ->forOwner(auth()->id(), get_class(auth()->user()))
            ->active()
            ->whereHas('bot', function ($botQuery) use ($data) {
                $botQuery->where('uuid', $data['bot_uuid'])->active();
            })
            ->orderBy('updated_at', 'desc')
            ->get()
            ->map(function ($conversation) {
                $conversation->bot?->makeHidden(['id']);
                $conversation->bot_uuid = $conversation->bot?->uuid;
                $conversation->owner?->makeHidden(['id']);
                $conversation->messages->makeHidden(['id', 'conversation_id']);

                return $conversation->makeHidden(['id', 'bot_id', 'owner_id', 'owner_type']);
            });
    }

    /**
     * Get recent conversations for a user.
     */
    public function getRecentConversations(
        int $userId,
        string $userType,
        int $limit = 5
    ): Collection {
        return Conversation::query()
            ->with(['bot', 'latestMessage'])
            ->forOwner($userId, $userType)
            ->active()
            ->orderBy('last_message_at', 'desc')
            ->limit($limit)
            ->get();
    }

    public function createGreetingMessage(Conversation $conversation, Bot $bot): void
    {
        try {
            $messageService = app(\Modules\ChatBot\Services\MessageService::class);

            $messageService->createAssistantMessage([
                'conversation_id' => $conversation->id,
                'role' => MessageRole::ASSISTANT,
                'content' => $bot->getGreetingMessage(),
                'content_type' => ContentType::TEXT,
                'status' => MessageStatus::COMPLETED,
            ]);
        }   catch (\Exception $e) {
            logger()->error('Failed to create greeting message: ' . $e->getMessage());
        }
    }
}
