<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON><PERSON>\ChatBot\Http\Controllers\Auth\BotController;
use Modules\ChatBot\Http\Controllers\Auth\BotShareController;
use Modu<PERSON>\ChatBot\Http\Controllers\Auth\ConversationController;
use Modules\ChatBot\Http\Controllers\Auth\KnowledgeBaseController;
use Modules\ChatBot\Http\Controllers\Auth\MessageController;
use Modules\ChatBot\Http\Controllers\Auth\PromptController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Public API routes (no authentication required)
Route::prefix('v1')->group(function () {
    // Public bots (read-only access)
    Route::get('bots/public', [BotController::class, 'publicBots'])->name('api.bots.public');
});

// Authenticated API routes (user context)
Route::middleware(['auth:api'])->prefix('v1/auth')->name('auth.')->group(function () {
    // User bot management
    Route::get('bots', [BotController::class, 'index'])->name('bots.index');
    Route::post('bots', [BotController::class, 'store'])->name('bots.store');
    Route::get('bots/{uuid}', [BotController::class, 'getByUuid'])->name('bots.uuid');
    Route::post('bots/{uuid}', [BotController::class, 'update'])->name('bots.update');


    // Prompt General
    Route::get('bot-general-prompt', [PromptController::class, 'getBotGeneralPrompt'])->name('bot-general-prompt');

});

// Authenticated API routes (user context)
Route::middleware(['auth:api'])->prefix('v1/auth/chat')->name('auth.')->group(function () {
    // User bot management
    Route::get('bots', [BotController::class, 'index'])->name('bots.index');


    // Prompt General
    Route::get('bot-general-prompt', [PromptController::class, 'getBotGeneralPrompt'])->name('bot-general-prompt');

});


// Authenticated API routes (user context)
Route::middleware(['auth:api'])->prefix('v1/auth/conversations')->name('auth.conversations.')->group(function () {
    // Conversation management
    Route::get('/', [ConversationController::class, 'index'])->name('index');
    Route::post('/', [ConversationController::class, 'createOrUpdateConversation'])->name('store');

    // Message management for conversations
    Route::get('{uuid}/messages', [MessageController::class, 'index'])->name('messages.index');
    Route::post('{uuid}/messages', [MessageController::class, 'store'])->name('messages.store');
    Route::get('{uuid}/messages/{messageUuid}', [MessageController::class, 'show'])->name('messages.show');
    Route::put('{uuid}/messages/{messageUuid}', [MessageController::class, 'update'])->name('messages.update');
    Route::delete('{uuid}/messages/{messageUuid}', [MessageController::class, 'destroy'])->name('messages.destroy');

    // Additional message endpoints
    Route::get('{uuid}/messages/role/{role}', [MessageController::class, 'messagesByRole'])->name('messages.by-role');
    Route::get('{uuid}/messages/failed', [MessageController::class, 'failedMessages'])->name('messages.failed');
});


// Admin API routes can be added later if needed
// Route::middleware(['auth', 'admin'])->prefix('v1/admin')->name('admin.')->group(function () {
//     // Admin bot management routes
// });
