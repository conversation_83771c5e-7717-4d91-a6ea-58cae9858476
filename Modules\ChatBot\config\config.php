<?php

return [
    'name' => 'ChatBot',

    /*
    |--------------------------------------------------------------------------
    | Facades Configuration
    |--------------------------------------------------------------------------
    */
    'facades' => [
        'Message' => \Modules\ChatBot\Facades\MessageFacade::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Async Processing Configuration
    |--------------------------------------------------------------------------
    */
    'async' => [
        'enabled' => env('CHATBOT_ASYNC_ENABLED', true),
        'default_queue' => env('CHATBOT_DEFAULT_QUEUE', 'ai-processing'),
        'streaming_queue' => env('CHATBOT_STREAMING_QUEUE', 'ai-streaming'),
        'timeout' => env('CHATBOT_PROCESSING_TIMEOUT', 300), // 5 minutes
        'retry_attempts' => env('CHATBOT_RETRY_ATTEMPTS', 3),
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook Configuration
    |--------------------------------------------------------------------------
    */
    'webhook' => [
        'url' => env('CHATBOT_WEBHOOK_URL'),
        'secret' => env('CHATBOT_WEBHOOK_SECRET'),
        'timeout' => env('CHATBOT_WEBHOOK_TIMEOUT', 10),
        'retry_attempts' => env('CHATBOT_WEBHOOK_RETRY_ATTEMPTS', 3),
    ],

    /*
    |--------------------------------------------------------------------------
    | Real-time Broadcasting Configuration
    |--------------------------------------------------------------------------
    */
    'broadcasting' => [
        'enabled' => env('CHATBOT_BROADCASTING_ENABLED', true),
        'redis_prefix' => env('CHATBOT_REDIS_PREFIX', 'chatbot:broadcast:'),
        'connection_timeout' => env('CHATBOT_CONNECTION_TIMEOUT', 3600), // 1 hour
        'chunk_storage_ttl' => env('CHATBOT_CHUNK_STORAGE_TTL', 3600), // 1 hour
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting Configuration
    |--------------------------------------------------------------------------
    */
    'rate_limits' => [
        'message_creation' => env('CHATBOT_MESSAGE_RATE_LIMIT', '60,1'), // 60 per minute
        'ai_generation' => env('CHATBOT_AI_RATE_LIMIT', '20,1'), // 20 per minute
        'streaming' => env('CHATBOT_STREAMING_RATE_LIMIT', '10,1'), // 10 per minute
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'max_context_messages' => env('CHATBOT_MAX_CONTEXT_MESSAGES', 50),
        'max_streaming_chunks' => env('CHATBOT_MAX_STREAMING_CHUNKS', 1000),
        'cleanup_interval' => env('CHATBOT_CLEANUP_INTERVAL', 3600), // 1 hour
    ],
];
