<?php

namespace Modules\ChatBot\Services;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\MessageStatus;
use Modules\ChatBot\Enums\ContentType;
use Modules\Core\Exceptions\BusinessException;
use Illuminate\Support\Facades\Log;

class MessageService
{
    public function __construct() {

    }

    /**
     * Create a new user message with UUID support.
     */
    public function createUserMessage(array $data): Message
    {
        // Support both conversation_id and conversation_uuid
        $conversation = null;
        if (isset($data['conversation_uuid'])) {
            $conversation = Conversation::where('uuid', $data['conversation_uuid'])->firstOrFail();
            $data['conversation_id'] = $conversation->id;
        } else {
            $conversation = Conversation::findOrFail($data['conversation_id']);
        }

        if (!$conversation->status->isEditable()) {
            throw new BusinessException('Cannot add messages to this conversation.');
        }

        $message = Message::create([
            'conversation_id' => $data['conversation_id'],
            'conversation_uuid' => $conversation->uuid,
            'role' => MessageRole::USER,
            'content' => $data['content'],
            'content_type' => $data['content_type'] ?? ContentType::TEXT,
            'attachments' => $data['attachments'] ?? null,
            'status' => MessageStatus::COMPLETED,
            'metadata' => $data['metadata'] ?? null,
        ]);

        // Update conversation stats
        $conversation->updateMessageStats();

        return $message->load('conversation');
    }

    /**
     * Create an assistant message (AI response) with UUID support.
     */
    public function createAssistantMessage(array $data): Message
    {
        // Support both conversation_id and conversation_uuid
        $conversation = null;
        if (isset($data['conversation_uuid'])) {
            $conversation = Conversation::where('uuid', $data['conversation_uuid'])->firstOrFail();
            $data['conversation_id'] = $conversation->id;
        } else {
            $conversation = Conversation::findOrFail($data['conversation_id']);
        }

        $message = Message::create([
            'conversation_id' => $data['conversation_id'],
            'conversation_uuid' => $conversation->uuid,
            'role' => MessageRole::ASSISTANT,
            'content' => $data['content'] ?? null,
            'content_type' => $data['content_type'] ?? ContentType::TEXT,
            'model_used' => $data['model_used'] ?? null,
            'status' => $data['status'] ?? MessageStatus::PENDING,
            'tool_calls' => $data['tool_calls'] ?? null,
            'attachments' => $data['attachments'] ?? null,
            'prompt_tokens' => $data['prompt_tokens'] ?? null,
            'completion_tokens' => $data['completion_tokens'] ?? null,
            'total_tokens' => $data['total_tokens'] ?? null,
            'cost' => $data['cost'] ?? null,
            'started_at' => $data['started_at'] ?? now(),
            'metadata' => $data['metadata'] ?? null,
        ]);

        return $message->load('conversation');
    }

    /**
     * Generate AI response for a conversation.
     */
    public function generateAIResponse(Conversation $conversation, array $options = []): Message
    {
        $bot = $conversation->bot;
        
        if (!$bot->isActive()) {
            throw new BusinessException('Bot is not active.');
        }

        // Create pending assistant message
        $message = $this->createAssistantMessage([
            'conversation_id' => $conversation->id,
            'model_used' => $bot->aiModel->key,
            'status' => MessageStatus::PENDING,
            'started_at' => now(),
        ]);

        try {
            // Get conversation context
            $context = $this->buildConversationContext($conversation);
            
            // Generate AI response
            $response = $this->aiService->generateResponse(
                $bot,
                $context,
                $options
            );

            // Update message with response
            $message->update([
                'content' => $response['content'],
                'status' => MessageStatus::COMPLETED,
                'tool_calls' => $response['tool_calls'] ?? null,
                'prompt_tokens' => $response['usage']['prompt_tokens'] ?? null,
                'completion_tokens' => $response['usage']['completion_tokens'] ?? null,
                'total_tokens' => $response['usage']['total_tokens'] ?? null,
                'cost' => $response['cost'] ?? null,
                'response_time_ms' => $response['response_time_ms'] ?? null,
                'completed_at' => now(),
            ]);

            // Handle tool calls if present
            if (!empty($response['tool_calls'])) {
                $this->handleToolCalls($message, $response['tool_calls'], $bot);
            }

            // Update conversation stats
            $conversation->updateMessageStats();

        } catch (\Exception $e) {
            $message->markAsFailed($e->getMessage(), get_class($e));
            throw $e;
        }

        return $message->fresh();
    }

    /**
     * Get messages for a conversation.
     * Latest messages first (page 1 = newest, page 2 = older, etc.)
     */
    public function getConversationMessages(string $conversationUuid): LengthAwarePaginator
    {
        $query = Message::query()
            ->whereHas('conversation', function ($q) use ($conversationUuid) {
                $q->where('uuid', $conversationUuid)
                  ->active()
                  ->forOwner(auth()->id(), get_class(auth()->user()));
            })
            ->where('status', MessageStatus::COMPLETED)
            ->orderBy('created_at', 'desc')
            ->paginate(50);
    }

    /**
     * Update a message.
     */
    public function updateMessage(Message $message, array $data): Message
    {
        // Only allow updating certain fields
        $allowedFields = [
            'content', 'attachments', 'metadata', 
            'quality_score', 'is_helpful'
        ];

        $updateData = array_intersect_key($data, array_flip($allowedFields));
        $message->update($updateData);

        return $message->fresh();
    }

    /**
     * Delete a message.
     */
    public function deleteMessage(Message $message): bool
    {
        $conversation = $message->conversation;
        $deleted = $message->delete();

        if ($deleted) {
            // Update conversation stats
            $conversation->updateMessageStats();
        }

        return $deleted;
    }

    /**
     * Retry a failed message.
     */
    public function retryMessage(Message $message): Message
    {
        if (!$message->status->isFailure()) {
            throw new BusinessException('Only failed messages can be retried.');
        }

        // Reset message status
        $message->update([
            'status' => MessageStatus::PENDING,
            'error_message' => null,
            'error_code' => null,
            'started_at' => now(),
            'completed_at' => null,
        ]);

        // Regenerate response if it's an assistant message
        if ($message->role === MessageRole::ASSISTANT) {
            return $this->regenerateAssistantMessage($message);
        }

        return $message->fresh();
    }

    /**
     * Build conversation context for AI.
     */
    public function buildConversationContext(Conversation $conversation): array
    {
        $messages = $conversation->messages()
            ->whereIn('role', [MessageRole::USER, MessageRole::ASSISTANT, MessageRole::SYSTEM])
            ->where('status', MessageStatus::COMPLETED)
            ->orderBy('created_at')
            ->get();

        $context = [];

        // Add system prompt
        if ($conversation->bot->system_prompt) {
            $context[] = [
                'role' => 'system',
                'content' => $conversation->bot->system_prompt,
            ];
        }

        // Add conversation messages
        foreach ($messages as $message) {
            $contextMessage = [
                'role' => $message->role->value,
                'content' => $message->content,
            ];

            if ($message->hasToolCalls()) {
                $contextMessage['tool_calls'] = $message->tool_calls;
            }

            $context[] = $contextMessage;
        }

        return $context;
    }

    /**
     * Regenerate assistant message.
     */
    private function regenerateAssistantMessage(Message $message): Message
    {
        $conversation = $message->conversation;
        
        try {
            // Get conversation context up to this message
            $context = $this->buildConversationContext($conversation);
            
            // Generate new response
            $response = $this->aiService->generateResponse(
                $conversation->bot,
                $context
            );

            // Update message
            $message->update([
                'content' => $response['content'],
                'status' => MessageStatus::COMPLETED,
                'tool_calls' => $response['tool_calls'] ?? null,
                'prompt_tokens' => $response['usage']['prompt_tokens'] ?? null,
                'completion_tokens' => $response['usage']['completion_tokens'] ?? null,
                'total_tokens' => $response['usage']['total_tokens'] ?? null,
                'cost' => $response['cost'] ?? null,
                'response_time_ms' => $response['response_time_ms'] ?? null,
                'completed_at' => now(),
            ]);

        } catch (\Exception $e) {
            $message->markAsFailed($e->getMessage(), get_class($e));
            throw $e;
        }

        return $message->fresh();
    }

    /**
     * Get message statistics.
     */
    public function getMessageStats(array $filters = []): array
    {
        $query = Message::query();

        // Apply filters
        if (!empty($filters['conversation_id'])) {
            $query->forConversation($filters['conversation_id']);
        }

        if (!empty($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        return [
            'total_messages' => $query->count(),
            'user_messages' => (clone $query)->fromUser()->count(),
            'assistant_messages' => (clone $query)->fromAssistant()->count(),
            'total_tokens' => $query->sum('total_tokens'),
            'total_cost' => $query->sum('cost'),
            'average_response_time' => $query->fromAssistant()->avg('response_time_ms'),
            'success_rate' => $this->calculateSuccessRate($query),
        ];
    }

    /**
     * Calculate success rate for messages.
     */
    private function calculateSuccessRate($query): float
    {
        $total = (clone $query)->fromAssistant()->count();
        if ($total === 0) return 0;

        $successful = (clone $query)->fromAssistant()->completed()->count();
        return round(($successful / $total) * 100, 2);
    }

    /**
     * Handle tool calls from AI response.
     */
    private function handleToolCalls(Message $message, array $toolCalls, Bot $bot): void
    {
        try {
            // Execute tool calls
            $toolResults = $this->aiService->executeToolCalls($toolCalls, $bot);

            // Create tool response messages
            foreach ($toolResults as $toolResult) {
                $this->createToolMessage([
                    'conversation_id' => $message->conversation_id,
                    'tool_call_id' => $toolResult['tool_call_id'],
                    'content' => $toolResult['content'],
                    'metadata' => [
                        'tool_name' => $toolResult['name'],
                        'parent_message_id' => $message->id,
                    ],
                ]);
            }

            // If tools were executed, generate a follow-up response
            if (!empty($toolResults)) {
                $this->generateFollowUpResponse($message->conversation, $bot);
            }

        } catch (\Exception $e) {
            Log::error('Tool execution failed', [
                'message_id' => $message->id,
                'tool_calls' => $toolCalls,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Create a tool response message with UUID support.
     */
    private function createToolMessage(array $data): Message
    {
        $conversation = Conversation::findOrFail($data['conversation_id']);

        return Message::create([
            'conversation_id' => $data['conversation_id'],
            'conversation_uuid' => $conversation->uuid,
            'role' => MessageRole::TOOL,
            'content' => $data['content'],
            'content_type' => ContentType::TEXT,
            'tool_call_id' => $data['tool_call_id'],
            'status' => MessageStatus::COMPLETED,
            'metadata' => $data['metadata'] ?? null,
        ]);
    }

    /**
     * Generate follow-up response after tool execution.
     */
    private function generateFollowUpResponse(Conversation $conversation, Bot $bot): void
    {
        try {
            // Get updated conversation context including tool results
            $context = $this->buildConversationContext($conversation);

            // Generate follow-up response
            $response = $this->aiService->generateResponse($bot, $context);

            // Create follow-up message
            $followUpMessage = $this->createAssistantMessage([
                'conversation_id' => $conversation->id,
                'model_used' => $bot->aiModel->key,
                'status' => MessageStatus::COMPLETED,
                'content' => $response['content'],
                'prompt_tokens' => $response['usage']['prompt_tokens'] ?? null,
                'completion_tokens' => $response['usage']['completion_tokens'] ?? null,
                'total_tokens' => $response['usage']['total_tokens'] ?? null,
                'cost' => $response['cost'] ?? null,
                'response_time_ms' => $response['response_time_ms'] ?? null,
                'started_at' => now(),
                'completed_at' => now(),
                'metadata' => ['is_follow_up' => true],
            ]);

        } catch (\Exception $e) {
            Log::error('Follow-up response generation failed', [
                'conversation_id' => $conversation->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
